import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../../core/theme/app_theme.dart';

class RecentActivities extends StatelessWidget {
  const RecentActivities({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Mock data for recent activities
    final activities = [
      {
        'title': 'Physics Test Completed',
        'subtitle': 'Score: 85/100',
        'time': '2 hours ago',
        'icon': Icons.quiz_outlined,
        'color': AppTheme.successColor,
      },
      {
        'title': 'Assignment Submitted',
        'subtitle': 'Mathematics Chapter 5',
        'time': '1 day ago',
        'icon': Icons.assignment_turned_in_outlined,
        'color': AppTheme.primaryColor,
      },
      {
        'title': 'Class Attended',
        'subtitle': 'Chemistry - Organic Compounds',
        'time': '2 days ago',
        'icon': Icons.school_outlined,
        'color': AppTheme.secondaryColor,
      },
    ];

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activities',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to full activities page
                  },
                  child: Text(
                    'View All',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Activities List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length,
            separatorBuilder: (context, index) => const Divider(
              height: 1,
              indent: 16,
              endIndent: 16,
            ),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return _buildActivityItem(activity, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity, int index) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: (activity['color'] as Color).withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          activity['icon'] as IconData,
          color: activity['color'] as Color,
          size: 20,
        ),
      ),
      title: Text(
        activity['title'] as String,
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        activity['subtitle'] as String,
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
      trailing: Text(
        activity['time'] as String,
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textTertiary,
        ),
      ),
      onTap: () {
        // Handle activity tap
      },
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }
}
