

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_sound_record/flutter_sound_record.dart'; // Updated import
import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:permission_handler/permission_handler.dart';
import 'package:sasthra_mobile_app/core/config/app_config.dart';




// State management (unchanged)
final chatStateProvider = StateNotifierProvider<ChatState, ChatStateData>((ref) {
  return ChatState();
});

class ChatStateData {
  final String? userId;
  final String sessionId;
  String text;
  dynamic file;
  List<Map<String, dynamic>> messages;
  bool isRecording;
  bool isDancing;
  bool botClicked;
  dynamic notification;

  ChatStateData({
    this.userId,
    this.sessionId = 'session_123456',
    this.text = '',
    this.file,
    this.messages = const [],
    this.isRecording = false,
    this.isDancing = false,
    this.botClicked = false,
    this.notification,
  });

  ChatStateData copyWith({
    String? userId,
    String? sessionId,
    String? text,
    dynamic file,
    List<Map<String, dynamic>>? messages,
    bool? isRecording,
    bool? isDancing,
    bool? botClicked,
    dynamic notification,
  }) {
    return ChatStateData(
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      text: text ?? this.text,
      file: file ?? this.file,
      messages: messages ?? this.messages,
      isRecording: isRecording ?? this.isRecording,
      isDancing: isDancing ?? this.isDancing,
      botClicked: botClicked ?? this.botClicked,
      notification: notification ?? this.notification,
    );
  }
}

class ChatState extends StateNotifier<ChatStateData> {
  ChatState() : super(ChatStateData());

  void updateText(String value) => state = state.copyWith(text: value);
  void updateFile(dynamic value) => state = state.copyWith(file: value);
  void updateMessages(List<Map<String, dynamic>> value) =>
      state = state.copyWith(messages: value);
  void updateIsRecording(bool value) =>
      state = state.copyWith(isRecording: value);
  void updateIsDancing(bool value) => state = state.copyWith(isDancing: value);
  void updateBotClicked(bool value) =>
      state = state.copyWith(botClicked: value);
}

class ChatSupport extends ConsumerWidget {
  final bool isVisible;
  final VoidCallback? onClose;

  const ChatSupport({super.key, this.isVisible = true, this.onClose});

  // Updated startRecording method for flutter_sound_record
  Future<void> startRecording(BuildContext context, WidgetRef ref) async {
    try {
      // Request microphone permission
      if (await Permission.microphone.request().isGranted) {
        final recorder = FlutterSoundRecord(); // Use FlutterSoundRecord
        final directory = await getApplicationDocumentsDirectory();
        final path = '${directory.path}/recording.wav';

        await recorder.start(
          path: path, // Specify the file path
        );

        final notifier = ref.read(chatStateProvider.notifier);
        notifier.updateIsRecording(true);
        notifier.updateIsDancing(true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Microphone permission denied.')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Failed to start recording. Check permissions.')),
      );
    }
  }

  // Updated stopRecording method for flutter_sound_record
  Future<void> stopRecording(WidgetRef ref) async {
    final notifier = ref.read(chatStateProvider.notifier);
    final state = ref.read(chatStateProvider);

    if (state.isRecording) {
      final recorder = FlutterSoundRecord(); // Use FlutterSoundRecord
      final path = await recorder.stop(); // Stop recording and get path
      await recorder.dispose(); // Dispose of the recorder

      notifier.updateFile(path);
      notifier.updateIsRecording(false);
      notifier.updateIsDancing(false);
    }
  }

  Future<void> handleSubmit(BuildContext context, WidgetRef ref) async {
    final state = ref.read(chatStateProvider);
    final notifier = ref.read(chatStateProvider.notifier);

    if (state.text.isEmpty && state.file == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please provide text, an image, or audio.')),
      );
      return;
    }

    notifier.updateText('');
    notifier.updateIsDancing(true);

    final formData = <String, String>{};
    if (state.text.isNotEmpty) formData['text'] = state.text;
    if (state.file != null) formData['file'] = state.file.toString();

    try {
      final userMessage = {
        'type': 'user',
        'text': state.text.isNotEmpty ? state.text : 'Audio uploaded',
        'timestamp': DateTime.now().toIso8601String(),
      };

      var updatedMessages = [...state.messages, userMessage];
      notifier.updateMessages(updatedMessages);

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}${AppConfig.chatBotEndpoint}'),
        body: formData,
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final cleanedResponse =
            cleanLatexText(jsonResponse['response'] as String);

        final aiMessage = {
          'type': 'ai',
          'text': cleanedResponse,
          'timestamp': DateTime.now().toIso8601String(),
        };

        updatedMessages = [...updatedMessages, aiMessage];
        notifier.updateMessages(updatedMessages);
      } else {
        throw Exception('Failed to process query');
      }
    } catch (e) {
      final errorMessage = {
        'type': 'error',
        'text': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      notifier.updateMessages([...state.messages, errorMessage]);
    } finally {
      notifier.updateFile(null);
      notifier.updateIsDancing(false);
    }
  }

  String cleanLatexText(String text) {
    return text
        .replaceAllMapped(RegExp(r'\$.*?\$'), (match) => match.group(0)!)
        .trim();
  }

  String formatTimestamp(String timestamp) {
    return DateTime.parse(timestamp).toLocal().toString();
  }

  Widget renderMessageText(String text) {
    final paragraphs =
        text.split('\n\n').where((p) => p.trim().isNotEmpty).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paragraphs.map((paragraph) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: TeXView(
            child: TeXViewDocument(paragraph.trim()),
            style: const TeXViewStyle(),
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(chatStateProvider);
    final notifier = ref.read(chatStateProvider.notifier);

    if (!isVisible) return const SizedBox.shrink();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      child: Stack(
        children: [
          Positioned(
            bottom: 24,
            right: 24,
            child: GestureDetector(
              onTap: () {
                notifier.updateBotClicked(true);
                notifier.updateIsDancing(true);
                Future.delayed(const Duration(milliseconds: 3000), () {
                  notifier.updateBotClicked(false);
                  notifier.updateIsDancing(false);
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                        blurRadius: 10, color: Colors.grey.withOpacity(0.3)),
                  ],
                ),
                child: state.isDancing
                    ? const Icon(Icons.favorite, color: Colors.red, size: 24)
                    : const Icon(Icons.chat, color: Colors.blue, size: 24),
              ),
            ),
          ),
          if (state.botClicked)
            Positioned(
              bottom: 100,
              right: 24,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text('Ready to help! 🎉',
                    style: TextStyle(color: Colors.blue)),
              ),
            ),
          Container(
            margin: const EdgeInsets.only(bottom: 80, right: 24),
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              child: Column(
                children: [
                  AppBar(
                    title: const Text('DoubtDesk'),
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: onClose,
                      ),
                    ],
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: state.messages.length,
                      itemBuilder: (context, index) {
                        final msg = state.messages[index];
                        return ListTile(
                          leading: msg['type'] == 'ai'
                              ? const Icon(Icons.android)
                              : const Icon(Icons.person),
                          title: renderMessageText(msg['text']),
                          subtitle: Text(formatTimestamp(msg['timestamp'])),
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: TextEditingController(text: state.text),
                            onChanged: (value) => notifier.updateText(value),
                            decoration: InputDecoration(
                              hintText: 'Type and press [enter]',
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20)),
                            ),
                          ),
                        ),
                        IconButton(
                          icon:
                              Icon(state.isRecording ? Icons.stop : Icons.mic),
                          onPressed: () {
                            if (state.isRecording) {
                              stopRecording(ref);
                            } else {
                              startRecording(context, ref);
                            }
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.send),
                          onPressed: () => handleSubmit(context, ref),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}