import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class StudentLiveStreamingPage extends StatefulWidget {
  const StudentLiveStreamingPage({super.key});

  @override
  State<StudentLiveStreamingPage> createState() => _StudentLiveStreamingPageState();
}

class _StudentLiveStreamingPageState extends State<StudentLiveStreamingPage> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _liveStreams = [];
  List<Map<String, dynamic>> _upcomingStreams = [];

  @override
  void initState() {
    super.initState();
    _loadStreams();
  }

  Future<void> _loadStreams() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _liveStreams = [
        {
          'id': '1',
          'title': 'Physics - Mechanics Revision',
          'teacher': 'Dr<PERSON> <PERSON>',
          'subject': 'Physics',
          'viewers': 234,
          'duration': '45 min',
          'thumbnail': 'https://via.placeholder.com/300x200',
          'isLive': true,
        },
        {
          'id': '2',
          'title': 'Chemistry - Organic Reactions',
          'teacher': 'Prof. Gupta',
          'subject': 'Chemistry',
          'viewers': 189,
          'duration': '60 min',
          'thumbnail': 'https://via.placeholder.com/300x200',
          'isLive': true,
        },
      ];
      
      _upcomingStreams = [
        {
          'id': '3',
          'title': 'Mathematics - Calculus Deep Dive',
          'teacher': 'Dr. Patel',
          'subject': 'Mathematics',
          'scheduledTime': DateTime.now().add(const Duration(hours: 2)),
          'duration': '90 min',
          'thumbnail': 'https://via.placeholder.com/300x200',
          'isLive': false,
        },
        {
          'id': '4',
          'title': 'Biology - Cell Structure',
          'teacher': 'Dr. Singh',
          'subject': 'Biology',
          'scheduledTime': DateTime.now().add(const Duration(hours: 5)),
          'duration': '75 min',
          'thumbnail': 'https://via.placeholder.com/300x200',
          'isLive': false,
        },
      ];
      
      _isLoading = false;
    });
    
    AppLogger.userAction('Live streams loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Live Streaming',
      subtitle: 'Join live classes and watch recordings',
      breadcrumbs: const ['Dashboard', 'Student', 'Live Streaming'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadStreams,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: _buildStreamingContent(),
    );
  }

  Widget _buildStreamingContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLiveNowSection(),
          const SizedBox(height: 24),
          _buildUpcomingSection(),
          const SizedBox(height: 24),
          _buildSubjectFilters(),
        ],
      ),
    );
  }

  Widget _buildLiveNowSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Live Now',
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_liveStreams.length} streams',
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_liveStreams.isEmpty)
          _buildEmptyState('No live streams', 'Check back later for live classes')
        else
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _liveStreams.length,
              itemBuilder: (context, index) {
                final stream = _liveStreams[index];
                return _buildStreamCard(stream, index, true);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildUpcomingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upcoming Classes',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (_upcomingStreams.isEmpty)
          _buildEmptyState('No upcoming classes', 'New classes will appear here')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _upcomingStreams.length,
            itemBuilder: (context, index) {
              final stream = _upcomingStreams[index];
              return _buildUpcomingStreamCard(stream, index);
            },
          ),
      ],
    );
  }

  Widget _buildStreamCard(Map<String, dynamic> stream, int index, bool isLive) {
    return Container(
      width: 280,
      margin: EdgeInsets.only(right: index < _liveStreams.length - 1 ? 16 : 0),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: const Icon(
                  Icons.play_circle_fill,
                  size: 48,
                  color: Colors.white,
                ),
              ),
              if (isLive)
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'LIVE',
                          style: AppTheme.bodySmall.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (isLive)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.visibility, color: Colors.white, size: 12),
                        const SizedBox(width: 4),
                        Text(
                          '${stream['viewers']}',
                          style: AppTheme.bodySmall.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  stream['title'],
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'by ${stream['teacher']}',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getSubjectColor(stream['subject']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        stream['subject'],
                        style: AppTheme.bodySmall.copyWith(
                          color: _getSubjectColor(stream['subject']),
                          fontSize: 10,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      stream['duration'],
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textTertiary,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildUpcomingStreamCard(Map<String, dynamic> stream, int index) {
    final scheduledTime = stream['scheduledTime'] as DateTime;
    final timeUntil = scheduledTime.difference(DateTime.now());
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: _getSubjectColor(stream['subject']).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.schedule,
            color: _getSubjectColor(stream['subject']),
            size: 24,
          ),
        ),
        title: Text(
          stream['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'by ${stream['teacher']}',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.access_time, size: 14, color: AppTheme.textTertiary),
                const SizedBox(width: 4),
                Text(
                  _formatTimeUntil(timeUntil),
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getSubjectColor(stream['subject']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    stream['subject'],
                    style: AppTheme.bodySmall.copyWith(
                      color: _getSubjectColor(stream['subject']),
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () => _setReminder(stream),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            minimumSize: const Size(70, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          child: const Text('Remind', style: TextStyle(fontSize: 12)),
        ),
        onTap: () => _showStreamDetails(stream),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildSubjectFilters() {
    final subjects = ['All', 'Physics', 'Chemistry', 'Mathematics', 'Biology'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filter by Subject',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          children: subjects.map((subject) {
            return FilterChip(
              label: Text(subject),
              selected: subject == 'All',
              onSelected: (selected) {
                AppLogger.userAction('Subject filter selected', {'subject': subject});
              },
              selectedColor: AppTheme.primaryColor.withOpacity(0.2),
              checkmarkColor: AppTheme.primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.live_tv_outlined,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getSubjectColor(String subject) {
    switch (subject.toLowerCase()) {
      case 'physics':
        return Colors.blue;
      case 'chemistry':
        return Colors.green;
      case 'mathematics':
        return Colors.orange;
      case 'biology':
        return Colors.purple;
      default:
        return AppTheme.primaryColor;
    }
  }

  String _formatTimeUntil(Duration duration) {
    if (duration.inHours > 0) {
      return 'in ${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return 'in ${duration.inMinutes}m';
    }
  }

  void _setReminder(Map<String, dynamic> stream) {
    AppLogger.userAction('Stream reminder set', {'stream': stream['title']});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Reminder set for ${stream['title']}'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showStreamDetails(Map<String, dynamic> stream) {
    AppLogger.userAction('Stream details viewed', {'stream': stream['title']});
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stream['title'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'by ${stream['teacher']}',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Stream details, syllabus coverage, and additional resources will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
