// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
    );

Map<String, dynamic> _$LoginResponseToJson(LoginResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'success': instance.success,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      token: json['token'] as String,
      activeSessionId: json['active_session_id'] as String,
      user: UserData.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'token': instance.token,
      'active_session_id': instance.activeSessionId,
      'user': instance.user,
    };

UserData _$UserDataFromJson(Map<String, dynamic> json) => UserData(
      id: json['id'] as String,
      username: json['username'] as String,
      role: json['role'] as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      centerCode: json['center_code'] as String?,
      centerName: json['center_name'] as String?,
      centerId: json['center_id'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      studentId: json['student_id'] as String?,
      courseId: json['course_id'] as String?,
      courseName: json['course_name'] as String?,
      subjectId: json['subject_id'] as String?,
      subjectName: json['subject_name'] as String?,
      designation: json['designation'] as String?,
    );

Map<String, dynamic> _$UserDataToJson(UserData instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'role': instance.role,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'center_code': instance.centerCode,
      'center_name': instance.centerName,
      'center_id': instance.centerId,
      'phone': instance.phone,
      'address': instance.address,
      'student_id': instance.studentId,
      'course_id': instance.courseId,
      'course_name': instance.courseName,
      'subject_id': instance.subjectId,
      'subject_name': instance.subjectName,
      'designation': instance.designation,
    };

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
    };

OtpRequest _$OtpRequestFromJson(Map<String, dynamic> json) => OtpRequest(
      otp: json['otp'] as String,
    );

Map<String, dynamic> _$OtpRequestToJson(OtpRequest instance) =>
    <String, dynamic>{
      'otp': instance.otp,
    };

SessionValidationRequest _$SessionValidationRequestFromJson(
        Map<String, dynamic> json) =>
    SessionValidationRequest(
      userId: json['user_id'] as String,
      activeSessionId: json['active_session_id'] as String,
    );

Map<String, dynamic> _$SessionValidationRequestToJson(
        SessionValidationRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'active_session_id': instance.activeSessionId,
    };

LogoutRequest _$LogoutRequestFromJson(Map<String, dynamic> json) =>
    LogoutRequest(
      userId: json['user_id'] as String,
    );

Map<String, dynamic> _$LogoutRequestToJson(LogoutRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
    };
