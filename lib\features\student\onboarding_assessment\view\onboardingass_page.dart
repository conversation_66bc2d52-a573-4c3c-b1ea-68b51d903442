import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import '../controller/onboardingass_controller.dart';

class OnboardingAssessmentPage extends ConsumerStatefulWidget {
  final VoidCallback? onSkip;
  const OnboardingAssessmentPage({super.key, this.onSkip});

  @override
  ConsumerState<OnboardingAssessmentPage> createState() =>
      _OnboardingAssessmentPageState();
}

class _OnboardingAssessmentPageState
    extends ConsumerState<OnboardingAssessmentPage> {
  int currentQuestionIndex = 0;
  Map<int, String> answers = {};
  int timeLeft = 120;
  Timer? timer;

  @override
  void initState() {
    super.initState();
    _fetchAssessment();
  }

  Future<void> _fetchAssessment() async {
    final data = await ref.read(startAssessmentProvider.future);
    ref.read(assessmentDataProvider.notifier).state = data;
    _startTimer();
  }

  void _startTimer() {
    timer?.cancel();
    timeLeft = 120;
    timer = Timer.periodic(const Duration(seconds: 1), (t) {
      if (timeLeft <= 0) {
        t.cancel();
        _handleNextOrSubmit();
      } else {
        setState(() => timeLeft--);
      }
    });
  }

  void _handleOptionSelect(int questionNumber, String optionKey) {
    setState(() {
      answers[questionNumber] = optionKey;
    });
  }

  void _handleNext() {
    if (currentQuestionIndex <
        (ref.watch(assessmentDataProvider)?.questions.length ?? 0) - 1) {
      setState(() => currentQuestionIndex++);
      _startTimer();
    }
  }

  void _handlePrev() {
    if (currentQuestionIndex > 0) {
      setState(() => currentQuestionIndex--);
      _startTimer();
    }
  }

  Future<void> _handleSubmit() async {
    timer?.cancel();
    final data = ref.watch(assessmentDataProvider);
    final payload = {
      'assessment_id': data?.assessmentId,
      'answers': answers,
    };
    await ref.read(submitAssessmentProvider(payload).future);
    await ref.read(completeAssessmentProvider.future);
    widget.onSkip?.call();
  }

  void _handleNextOrSubmit() {
    final questions = ref.watch(assessmentDataProvider)?.questions ?? [];
    if (currentQuestionIndex < questions.length - 1) {
      _handleNext();
    } else {
      _handleSubmit();
    }
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final assessment = ref.watch(assessmentDataProvider);
    final currentQuestion = assessment?.questions[currentQuestionIndex];
    final totalQuestions = assessment?.questions.length ?? 0;
    final isLastQuestion = currentQuestionIndex == totalQuestions - 1;
    final progress =
        totalQuestions > 0 ? (currentQuestionIndex + 1) / totalQuestions : 0.0;
    final isAnswerSelected =
        answers.containsKey(currentQuestion?.questionNumber);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF5F7FF),
              Color(0xFFE6E9FF),
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Main Content Column
              Column(
                children: [
                  // Header Section with Glass Effect
                  ClipRRect(
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        padding: const EdgeInsets.only(
                          left: 24,
                          right: 24,
                          top: 50,
                          bottom: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.6),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 2,
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Onboarding Assessment',
                              style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.indigo),
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'This short quiz will help us understand your starting point',
                              style:
                                  TextStyle(fontSize: 14, color: Colors.indigo),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Progress Indicator
                                LinearPercentIndicator(
                                  width:
                                      MediaQuery.of(context).size.width - 180,
                                  lineHeight: 8,
                                  percent: progress,
                                  backgroundColor: Colors.grey[200],
                                  progressColor: Colors.indigo,
                                  barRadius: const Radius.circular(4),
                                  padding: EdgeInsets.zero,
                                ),

                                // Timer with Glass Effect
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: BackdropFilter(
                                    filter:
                                        ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.indigo[50]!.withOpacity(0.7),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(Icons.timer_outlined,
                                              size: 18, color: Colors.indigo),
                                          const SizedBox(width: 4),
                                          Text(
                                            _formatTime(timeLeft),
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.indigo[800]),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Question Content
                  if (ref.watch(startAssessmentProvider).isLoading ||
                      assessment == null)
                    const Expanded(
                      child: Center(child: CircularProgressIndicator()),
                    )
                  else
                    Expanded(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: KeyedSubtree(
                          key: ValueKey(currentQuestionIndex),
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.all(24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Section Tag with Glass Effect
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: BackdropFilter(
                                    filter:
                                        ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.indigo[50]!.withOpacity(0.7),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        (currentQuestion?.sectionType ?? '')
                                            .replaceAll('_', ' ')
                                            .toUpperCase(),
                                        style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.indigo[800]),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 24),

                                // Question Text
                                Text(
                                  'Q${currentQuestion?.questionNumber}: ${currentQuestion?.questionText}',
                                  style: const TextStyle(
                                      fontSize: 18,
                                      height: 1.4,
                                      color: Colors.indigo),
                                ),
                                const SizedBox(height: 16),

                                // Question Image
                                if (currentQuestion?.imageBase64 != null)
                                  Center(
                                    child: Container(
                                      margin: const EdgeInsets.only(bottom: 24),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.2),
                                            spreadRadius: 1,
                                            blurRadius: 8,
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: Image.memory(
                                          base64Decode(
                                              currentQuestion!.imageBase64!),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),

                                // Options Grid
                                GridView.count(
                                  crossAxisCount: 2,
                                  childAspectRatio: 3.2,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  mainAxisSpacing: 12,
                                  crossAxisSpacing: 12,
                                  padding: const EdgeInsets.only(bottom: 24),
                                  children: currentQuestion?.options.entries
                                          .map((entry) {
                                        final isSelected = answers[
                                                currentQuestion!
                                                    .questionNumber] ==
                                            entry.key;
                                        return InkWell(
                                          onTap: () => _handleOptionSelect(
                                              currentQuestion!.questionNumber,
                                              entry.key),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: isSelected
                                                  ? Colors.indigo
                                                  : Colors.white
                                                      .withOpacity(0.8),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color: isSelected
                                                    ? Colors.indigo
                                                    : Colors.grey[200]!,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.grey
                                                      .withOpacity(0.1),
                                                  blurRadius: 6,
                                                  offset: const Offset(0, 3),
                                                ),
                                              ],
                                            ),
                                            child: Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 12),
                                                child: Text(
                                                  '${entry.key}: ${entry.value}',
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                    color: isSelected
                                                        ? Colors.white
                                                        : Colors.indigo,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      }).toList() ??
                                      [],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                  // Navigation Footer with Glass Effect
                  ClipRRect(
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.6),
                          border: Border(
                            top: BorderSide(color: Colors.grey[200]!),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Previous Button
                            ElevatedButton(
                              onPressed:
                                  currentQuestionIndex > 0 ? _handlePrev : null,
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.grey[800],
                                backgroundColor:
                                    Colors.grey[100]!.withOpacity(0.8),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text('Previous'),
                            ),

                            // Question Counter
                            Text(
                              '${currentQuestionIndex + 1} of $totalQuestions',
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.indigo),
                            ),

                            // Next/Submit Button
                            ElevatedButton(
                              onPressed: isAnswerSelected
                                  ? (isLastQuestion
                                      ? _handleSubmit
                                      : _handleNext)
                                  : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.indigo,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(isLastQuestion ? 'Submit' : 'Next'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              Positioned(
                top: 10,
                right: 16,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 224, 71, 71)
                            .withOpacity(0.6),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: const Color.fromARGB(255, 224, 71, 71)
                              .withOpacity(0.2),
                        ),
                      ),
                      child: TextButton(
                        onPressed: widget.onSkip,
                        style: TextButton.styleFrom(
                          foregroundColor:
                              const Color.fromARGB(255, 232, 232, 233),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          minimumSize:
                              Size.zero, // removes default min size constraints
                          tapTargetSize: MaterialTapTargetSize
                              .shrinkWrap, // makes tap target smaller
                        ),
                        child: const Text(
                          'Ask me later',
                          style: TextStyle(fontSize: 12), // smaller font size
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
