/// auth_models.dart - Authentication Data Models
///
/// English: This file contains all data models related to authentication and user management
/// in the Sasthra application. It defines the structure for API responses, user data, and
/// authentication states. These models use JSON serialization for API communication and
/// provide type-safe data handling throughout the authentication flow.
///
/// Tanglish: Inga naama app oda authentication and user management ku vendiya ella data
/// models um irukku. API responses, user data, authentication states - ellam inga define
/// pannirukkom. JSON serialization use pannitu type-safe data handling provide pannum.
///
/// Key Models:
/// - LoginResponse: API response for login requests
/// - AuthResponse: Complete authentication response with token and user data
/// - UserData: User profile information and role details
/// - AuthState: Enumeration of authentication states
/// - AuthResult: Result wrapper for authentication operations
///
/// Features:
/// - JSON serialization/deserialization with code generation
/// - Type-safe data models with null safety
/// - Immutable data structures for state management
/// - Comprehensive user role and permission handling
/// - Error-safe data parsing and validation
///
/// Usage: Used throughout the app for authentication data handling and API communication
library;

import 'package:json_annotation/json_annotation.dart';

part 'auth_models.g.dart';

@JsonSerializable()
class LoginResponse {
  final String message;
  final bool success;

  LoginResponse({
    required this.message,
    this.success = true,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class AuthResponse {
  final String token;
  @JsonKey(name: 'active_session_id')
  final String activeSessionId;
  final UserData user;

  AuthResponse({
    required this.token,
    required this.activeSessionId,
    required this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class UserData {
  final String id;
  final String username;
  final String role;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  @JsonKey(name: 'center_code')
  final String? centerCode;
  @JsonKey(name: 'center_name')
  final String? centerName;
  @JsonKey(name: 'center_id')
  final String? centerId;
  final String? phone;
  final String? address;
  @JsonKey(name: 'student_id')
  final String? studentId;
  @JsonKey(name: 'course_id')
  final String? courseId;
  @JsonKey(name: 'course')
  final String? course;
  @JsonKey(name: 'subject_id')
  final String? subjectId;
  @JsonKey(name: 'subject_name')
  final String? subjectName;
  final String? designation;

  UserData({
    required this.id,
    required this.username,
    required this.role,
    this.firstName,
    this.lastName,
    this.centerCode,
    this.centerName,
    this.centerId,
    this.phone,
    this.address,
    this.studentId,
    this.courseId,
    this.course,
    this.subjectId,
    this.subjectName,
    this.designation,
  });

factory UserData.testUser() {
    return UserData(
      id: "test_id",
      username: "TESTUSER",
      role: "student",
      firstName: "Test",
      lastName: "User",
      centerName: "Demo Center",
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json) =>
      _$UserDataFromJson(json);

  Map<String, dynamic> toJson() => _$UserDataToJson(this);

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    if (firstName != null) return firstName!;
    if (centerName != null) return centerName!;
    return username;
  }

  String get roleDisplayName {
    switch (role) {
      case 'faculty':
        return 'Faculty';
      case 'kota_teacher':
        return 'Kota Teacher';
      case 'student':
        return 'Student';
      case 'director':
        return 'Director';
      case 'mendor':
        return 'Mentor';
      case 'center_counselor':
        return 'Center Counselor';
      case 'parent':
        return 'Parent';
      default:
        return role.toUpperCase();
    }
  }
}

@JsonSerializable()
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class OtpRequest {
  final String otp;

  OtpRequest({required this.otp});

  factory OtpRequest.fromJson(Map<String, dynamic> json) =>
      _$OtpRequestFromJson(json);

  Map<String, dynamic> toJson() => _$OtpRequestToJson(this);
}

@JsonSerializable()
class SessionValidationRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'active_session_id')
  final String activeSessionId;

  SessionValidationRequest({
    required this.userId,
    required this.activeSessionId,
  });

  factory SessionValidationRequest.fromJson(Map<String, dynamic> json) =>
      _$SessionValidationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SessionValidationRequestToJson(this);
}

@JsonSerializable()
class LogoutRequest {
  @JsonKey(name: 'user_id')
  final String userId;

  LogoutRequest({required this.userId});

  factory LogoutRequest.fromJson(Map<String, dynamic> json) =>
      _$LogoutRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LogoutRequestToJson(this);
}

/// Authentication state enum
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  otpRequired,
  error,
}

/// Authentication result class
class AuthResult {
  final bool success;
  final String? message;
  final AuthResponse? authResponse;
  final Exception? error;

  AuthResult({
    required this.success,
    this.message,
    this.authResponse,
    this.error,
  });

  factory AuthResult.success({
    String? message,
    AuthResponse? authResponse,
  }) {
    return AuthResult(
      success: true,
      message: message,
      authResponse: authResponse,
    );
  }

  factory AuthResult.failure({
    String? message,
    Exception? error,
  }) {
    return AuthResult(
      success: false,
      message: message,
      error: error,
    );
  }
}


// Add this at the bottom of auth_models.dart

@JsonSerializable()
class Faculty {
  final String email;
  @JsonKey(name: 'first_name')
  final String firstName;
  final String id;
  @JsonKey(name: 'last_name')
  final String lastName;
  final String phone;

  Faculty({
    required this.email,
    required this.firstName,
    required this.id,
    required this.lastName,
    required this.phone,
  });

  factory Faculty.fromJson(Map<String, dynamic> json) => _$FacultyFromJson(json);
  Map<String, dynamic> toJson() => _$FacultyToJson(this);
}

@JsonSerializable()
class Parent {
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  @JsonKey(name: 'parent_email')
  final String parentEmail;
  final String phone;
  final String relationship;

  Parent({
    required this.firstName,
    required this.lastName,
    required this.parentEmail,
    required this.phone,
    required this.relationship,
  });

  factory Parent.fromJson(Map<String, dynamic> json) => _$ParentFromJson(json);
  Map<String, dynamic> toJson() => _$ParentToJson(this);
}

@JsonSerializable()
class StudentData {
  final String id;
  final String username;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  final String? phone;
  @JsonKey(name: 'student_email')
  final String? studentEmail;
  @JsonKey(name: 'full_address')
  final String? fullAddress;
  @JsonKey(name: 'aadhar_number')
  final String? aadharNumber;
  final int? age;
  @JsonKey(name: 'batch_id')
  final String? batchId;
  @JsonKey(name: 'batch_name')
  final String? batchName;
  @JsonKey(name: 'center_code')
  final String? centerCode;
  @JsonKey(name: 'center_name')
  final String? centerName;
  @JsonKey(name: 'center_phone')
  final String? centerPhone;
  final String? course;
  @JsonKey(name: 'course_id')
  final String? courseId;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  final String? dob;
  final String? gender;
  final String? grade;
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @JsonKey(name: 'marks_10th')
  final String? marks10th;
  @JsonKey(name: 'marks_12th')
  final String? marks12th;
  final String? nationality;
  final String? religion;
  @JsonKey(name: 'student_face_embedding')
  final String? studentFaceEmbedding;

  StudentData({
    required this.id,
    required this.username,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.studentEmail,
    this.fullAddress,
    this.aadharNumber,
    this.age,
    this.batchId,
    this.batchName,
    this.centerCode,
    this.centerName,
    this.centerPhone,
    this.course,
    this.courseId,
    this.createdAt,
    this.dob,
    this.gender,
    this.grade,
    this.isActive,
    this.marks10th,
    this.marks12th,
    this.nationality,
    this.religion,
    this.studentFaceEmbedding,
  });

  factory StudentData.fromJson(Map<String, dynamic> json) => _$StudentDataFromJson(json);
  Map<String, dynamic> toJson() => _$StudentDataToJson(this);

  String get displayName => '$firstName $lastName';
}

@JsonSerializable()
class StudentDashboardResponse {
  final List<Faculty> faculty;
  @JsonKey(name: 'kota_teachers')
  final List<dynamic> kotaTeachers; // Empty list in response
  final Parent parent;
  final StudentData student;

  StudentDashboardResponse({
    required this.faculty,
    required this.kotaTeachers,
    required this.parent,
    required this.student,
  });

  factory StudentDashboardResponse.fromJson(Map<String, dynamic> json) =>
      _$StudentDashboardResponseFromJson(json);
  Map<String, dynamic> toJson() => _$StudentDashboardResponseToJson(this);
}



