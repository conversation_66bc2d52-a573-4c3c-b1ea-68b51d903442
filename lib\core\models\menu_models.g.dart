// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menu_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MenuItem _$MenuItemFromJson(Map<String, dynamic> json) => MenuItem(
      href: json['href'] as String,
      name: json['name'] as String,
      orderby: (json['orderby'] as num).toInt(),
      role: json['role'] as String,
      submenu: (json['submenu'] as List<dynamic>?)
          ?.map((e) => MenuItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      uuid: json['uuid'] as String,
    );

Map<String, dynamic> _$MenuItemToJson(MenuItem instance) => <String, dynamic>{
      'href': instance.href,
      'name': instance.name,
      'orderby': instance.orderby,
      'role': instance.role,
      'submenu': instance.submenu,
      'uuid': instance.uuid,
    };

MenuCache _$MenuCacheFromJson(Map<String, dynamic> json) => MenuCache(
      role: json['role'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => MenuItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      cachedAt: DateTime.parse(json['cachedAt'] as String),
      cacheExpiry: json['cacheExpiry'] == null
          ? const Duration(hours: 24)
          : Duration(microseconds: (json['cacheExpiry'] as num).toInt()),
    );

Map<String, dynamic> _$MenuCacheToJson(MenuCache instance) => <String, dynamic>{
      'role': instance.role,
      'items': instance.items,
      'cachedAt': instance.cachedAt.toIso8601String(),
      'cacheExpiry': instance.cacheExpiry.inMicroseconds,
    };
