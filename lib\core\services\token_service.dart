import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';
import '../utils/logger.dart';
import '../models/auth_models.dart';
import 'storage_service.dart';
import 'secure_storage_service.dart';
import 'api_service.dart';

class TokenService {
  static final TokenService _instance = TokenService._internal();
  factory TokenService() => _instance;
  TokenService._internal();



  Timer? _refreshTimer;
  Timer? _periodicTimer;
  bool _isRefreshing = false;
  final List<Completer<bool>> _refreshCompleters = [];

  /// Initialize token service and start automatic refresh
  Future<void> initialize() async {
    try {
      await SecureStorageService.init();
      await _startTokenRefreshTimer();
      _startPeriodicValidation();
      AppLogger.info('TokenService initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize TokenService: $e');
    }
  }

  /// Store JWT token securely
  Future<void> storeToken(String token) async {
    try {
      await SecureStorageService.write(key: AppConfig.tokenKey, value: token);
      await _updateLastRefreshTime();
      await _startTokenRefreshTimer();
      AppLogger.info('Token stored successfully');
    } catch (e) {
      AppLogger.error('Failed to store token: $e');
      throw Exception('Failed to store authentication token');
    }
  }

  /// Get stored JWT token
  Future<String?> getToken() async {
    try {
      return await SecureStorageService.read(key: AppConfig.tokenKey);
    } catch (e) {
      AppLogger.error('Failed to retrieve token: $e');
      return null;
    }
  }

  /// Store session ID
  Future<void> storeSessionId(String sessionId) async {
    try {
      await SecureStorageService.write(key: AppConfig.sessionIdKey, value: sessionId);
      AppLogger.info('Session ID stored successfully');
    } catch (e) {
      AppLogger.error('Failed to store session ID: $e');
    }
  }

  /// Get stored session ID
  Future<String?> getSessionId() async {
    try {
      return await SecureStorageService.read(key: AppConfig.sessionIdKey);
    } catch (e) {
      AppLogger.error('Failed to retrieve session ID: $e');
      return null;
    }
  }

  /// Check if token exists and is valid
  Future<bool> isTokenValid() async {
    try {
      final token = await getToken();
      if (token == null || token.isEmpty) {
        return false;
      }

      // Check if token is expired
      if (JwtDecoder.isExpired(token)) {
        AppLogger.warning('Token is expired');
        return false;
      }

      // Check if token needs refresh (within buffer time)
      final expiryDate = JwtDecoder.getExpirationDate(token);
      final now = DateTime.now();
      final timeUntilExpiry = expiryDate.difference(now);

      if (timeUntilExpiry <= AppConfig.tokenExpiryBuffer) {
        AppLogger.info('Token needs refresh (expires in ${timeUntilExpiry.inMinutes} minutes)');
        return await validateAndRefreshToken();
      }

      return true;
    } catch (e) {
      AppLogger.error('Error validating token: $e');
      return false;
    }
  }

  /// Validate token with backend and refresh if needed
  Future<bool> validateAndRefreshToken() async {
    if (_isRefreshing) {
      // If already refreshing, wait for the current refresh to complete
      final completer = Completer<bool>();
      _refreshCompleters.add(completer);
      return completer.future;
    }

    _isRefreshing = true;

    try {
      final token = await getToken();
      if (token == null) {
        _completeRefreshCompleters(false);
        return false;
      }

      // First, try to validate with backend
      final apiService = ApiService();
      final isValid = await apiService.validateToken(token);

      if (isValid) {
        await _updateLastRefreshTime();
        _completeRefreshCompleters(true);
        return true;
      }

      // Token is invalid, clear storage and redirect to login
      await clearTokens();
      _completeRefreshCompleters(false);
      return false;

    } catch (e) {
      AppLogger.error('Token validation/refresh failed: $e');
      _completeRefreshCompleters(false);
      return false;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Clear all stored tokens and session data
  Future<void> clearTokens() async {
    try {
      await SecureStorageService.delete(key: AppConfig.tokenKey);
      await SecureStorageService.delete(key: AppConfig.sessionIdKey);
      await StorageService.remove(AppConfig.userDataKey);
      await StorageService.remove(AppConfig.lastTokenRefreshKey);
      await StorageService.remove(AppConfig.menuCacheKey);

      _stopTokenRefreshTimer();
      _stopPeriodicValidation();
      AppLogger.info('All tokens and session data cleared');
    } catch (e) {
      AppLogger.error('Failed to clear tokens: $e');
    }
  }

  /// Get token payload/claims
  Map<String, dynamic>? getTokenPayload() {
    try {
      final token = getToken();
      
      return JwtDecoder.decode(token.toString());
    } catch (e) {
      AppLogger.error('Failed to decode token payload: $e');
      return null;
    }
  }

  /// Start automatic token refresh timer
  Future<void> _startTokenRefreshTimer() async {
    _stopTokenRefreshTimer();

    try {
      final token = await getToken();
      if (token == null) return;

      final expiryDate = JwtDecoder.getExpirationDate(token);
      final now = DateTime.now();
      final timeUntilRefresh = expiryDate.difference(now) - AppConfig.tokenExpiryBuffer;

      if (timeUntilRefresh.isNegative) {
        // Token needs immediate refresh
        await validateAndRefreshToken();
        return;
      }

      _refreshTimer = Timer(timeUntilRefresh, () async {
        await validateAndRefreshToken();
      });

      AppLogger.info('Token refresh timer started (refresh in ${timeUntilRefresh.inMinutes} minutes)');
    } catch (e) {
      AppLogger.error('Failed to start token refresh timer: $e');
    }
  }

  /// Stop token refresh timer
  void _stopTokenRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Start periodic validation for web platforms
  void _startPeriodicValidation() {
    // For web platforms, use a periodic timer to validate tokens every hour
    if (kIsWeb) {
      _periodicTimer?.cancel();
      _periodicTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
        try {
          final isValid = await validateAndRefreshToken();
          if (!isValid) {
            AppLogger.info('Token validation failed, stopping periodic timer');
            timer.cancel();
          }
        } catch (e) {
          AppLogger.error('Periodic token validation failed: $e');
        }
      });
      AppLogger.info('Periodic token validation started for web platform');
    }
  }

  /// Stop periodic validation timer
  void _stopPeriodicValidation() {
    _periodicTimer?.cancel();
    _periodicTimer = null;
  }

  /// Update last token refresh timestamp
  Future<void> _updateLastRefreshTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        AppConfig.lastTokenRefreshKey,
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      AppLogger.error('Failed to update last refresh time: $e');
    }
  }

  /// Complete all pending refresh operations
  void _completeRefreshCompleters(bool result) {
    for (final completer in _refreshCompleters) {
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    }
    _refreshCompleters.clear();
  }

  /// Dispose resources
  void dispose() {
    _stopTokenRefreshTimer();
    _completeRefreshCompleters(false);
  }
}
