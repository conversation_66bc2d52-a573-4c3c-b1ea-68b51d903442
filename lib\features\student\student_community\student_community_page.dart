import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decode/jwt_decode.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart'; // For connectivity

// Import your AppConfig and ApiService
import '../../../core/config/app_config.dart'; // Your AppConfig
import '../../../core/services/api_service.dart'; // Your ApiService

// Models
class User {
  final String id;
  final String role;
  final String username;
  final String? type;

  User(
      {required this.id,
      required this.role,
      required this.username,
      this.type});

  factory User.fromJwt(String token) {
    print('Decoding token: $token');
    final payload = Jwt.parseJwt(token);
    return User(
      id: payload['user_id']?.toString() ?? 'unknown',
      role: payload['role'] ?? 'student',
      username: payload['username'] ?? 'User',
      type: payload['role']?.toString(),
    );
  }
}

class Thread {
  final String id;
  final String content;
  final String senderId;
  final String senderType;
  final String firstName;
  final DateTime createdAt;
  List<Reply> subthreads;

  Thread({
    required this.id,
    required this.content,
    required this.senderId,
    required this.senderType,
    required this.firstName,
    required this.createdAt,
    List<Reply>? subthreads,
  }) : subthreads = subthreads ?? [];

  factory Thread.fromJson(Map<String, dynamic> json) {
    return Thread(
      id: json['id'].toString(),
      content: json['content'] ?? '',
      senderId: json['sender_id'].toString(),
      senderType: json['sender_type'] ?? 'student',
      firstName: json['first_name'] ?? 'User',
      createdAt: DateTime.parse(json['created_at']),
      subthreads: (json['subthreads'] as List<dynamic>? ?? [])
          .map((r) => Reply.fromJson(r as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Reply {
  final String id;
  final String content;
  final String senderId;
  final String firstName;
  final DateTime createdAt;
  final String? parentId;

  Reply({
    required this.id,
    required this.content,
    required this.senderId,
    required this.firstName,
    required this.createdAt,
    this.parentId,
  });

  factory Reply.fromJson(Map<String, dynamic> json) {
    return Reply(
      id: json['id'].toString(),
      content: json['content'] ?? '',
      senderId: json['sender_id'].toString(),
      firstName: json['first_name'] ?? 'User',
      createdAt: DateTime.parse(json['created_at']),
      parentId: json['parent_id']?.toString(),
    );
  }
}

class ImageData {
  final String id;
  final String url;
  final List<Thread>? threads;

  ImageData({required this.id, required this.url, this.threads});

  factory ImageData.fromJson(Map<String, dynamic> json) {
    return ImageData(
      id: json['id'].toString(),
      url: json['url'] ?? '',
      threads: (json['threads'] as List<dynamic>? ?? [])
          .map((t) => Thread.fromJson(t as Map<String, dynamic>))
          .toList(),
    );
  }
}

// Community Provider (State Management)
class CommunityProvider extends ChangeNotifier {
  String? token;
  User? user;
  List<Thread> localThreads = [];
  List<ImageData> images = [];
  ImageData? selectedImage;
  bool loadingThreads = false;
  bool loadingImages = false;
  bool loadingFullImage = false;
  String newMessage = '';
  String newReply = '';
  Thread? selectedThread;
  final ApiService _apiService = ApiService();

  CommunityProvider() {
    print('CommunityProvider initialized');
    _loadToken();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadToken() async {
    print('Attempting to load token');
    final prefs = await SharedPreferences.getInstance();
    print('AppConfig.tokenKey: ${AppConfig.tokenKey}'); // Verify the key
    print(
        'All SharedPreferences keys: ${prefs.getKeys()}'); // List all stored keys
    token = prefs.getString(AppConfig.tokenKey);
    print('Token loaded: $token');
    final role = prefs.getString('role');
    print('Role loaded: $role');
    if (token != null) {
      try {
        user = User.fromJwt(token!);
        if (role != null)
          user = User(
              id: user!.id, role: role, username: user!.username, type: role);
        await _fetchThreads();
        await _fetchImages();
      } catch (e) {
        debugPrint('JWT decode error: $e');
        user = User(id: 'guest', role: 'guest', username: 'Guest');
      }
    }
    notifyListeners();
  }

  Future<void> login(String inputToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.tokenKey, inputToken.trim());
    await prefs.setString('role', user?.role ?? 'student');
    token = inputToken.trim();
    print('Login token set: $token');
    user = User.fromJwt(token!);
    await _fetchThreads();
    await _fetchImages();
    notifyListeners();
  }

  Future<void> _fetchThreads() async {
    loadingThreads = true;
    notifyListeners();
    try {
      final threadsData = await _apiService.getThreads();
      localThreads = threadsData.map((t) => Thread.fromJson(t)).toList();
    } catch (e) {
      debugPrint('Fetch threads error: $e');
    }
    loadingThreads = false;
    notifyListeners();
  }

  Future<void> _fetchImages() async {
    loadingImages = true;
    notifyListeners();
    try {
      final imagesData = await _apiService.getCommunityImages();
      images = imagesData.map((i) => ImageData.fromJson(i)).toList();
    } catch (e) {
      debugPrint('Fetch images error: $e');
    }
    loadingImages = false;
    notifyListeners();
  }

  Future<void> fetchFullImage(String id) async {
    loadingFullImage = true;
    notifyListeners();
    try {
      final imageData = await _apiService.getCommunityImageById(id);
      selectedImage = ImageData.fromJson(imageData);
    } catch (e) {
      debugPrint('Fetch full image error: $e');
    }
    loadingFullImage = false;
    notifyListeners();
  }

  Future<void> createThread(String content) async {
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
    final tempThread = Thread(
      id: tempId,
      content: content,
      senderId: user!.id,
      senderType: user!.role,
      firstName: user!.username,
      createdAt: DateTime.now(),
    );
    localThreads.add(tempThread);
    selectedThread = tempThread;
    notifyListeners();

    try {
      final threadData = await _apiService.createThread(content);
      final realThread = Thread.fromJson(threadData);
      localThreads.removeWhere((t) => t.id == tempId);
      localThreads.add(realThread);
      selectedThread = realThread;
      await _fetchThreads(); // Refetch for invalidation
    } catch (e) {
      localThreads.removeWhere((t) => t.id == tempId);
      debugPrint('Create thread error: $e');
    }
    notifyListeners();
  }

  Future<void> addReply(String content, String parentId) async {
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
    final tempReply = Reply(
      id: tempId,
      content: content,
      senderId: user!.id,
      firstName: user!.username,
      createdAt: DateTime.now(),
      parentId: parentId,
    );

    final threadIndex = localThreads.indexWhere((t) => t.id == parentId);
    if (threadIndex != -1) localThreads[threadIndex].subthreads.add(tempReply);
    if (selectedThread?.id == parentId)
      selectedThread!.subthreads.add(tempReply);
    notifyListeners();

    try {
      final replyData = await _apiService.addReply(content, parentId);
      final realReply = Reply.fromJson(replyData);
      if (threadIndex != -1) {
        final replyIndex = localThreads[threadIndex]
            .subthreads
            .indexWhere((r) => r.id == tempId);
        if (replyIndex != -1)
          localThreads[threadIndex].subthreads[replyIndex] = realReply;
      }
      if (selectedThread?.id == parentId) {
        final replyIndex =
            selectedThread!.subthreads.indexWhere((r) => r.id == tempId);
        if (replyIndex != -1)
          selectedThread!.subthreads[replyIndex] = realReply;
      }
      await _fetchThreads(); // Refetch
    } catch (e) {
      if (threadIndex != -1)
        localThreads[threadIndex].subthreads.removeWhere((r) => r.id == tempId);
      if (selectedThread?.id == parentId)
        selectedThread!.subthreads.removeWhere((r) => r.id == tempId);
      debugPrint('Add reply error: $e');
    }
    notifyListeners();
  }

  Future<void> uploadImage(XFile file) async {
    try {
      final fileData = File(file.path);
      await _apiService.uploadCommunityImage(fileData, user!.id);
      await _fetchImages(); // Refetch for invalidation
    } catch (e) {
      debugPrint('Upload error: $e');
    }
  }

  Future<void> addThreadToImage(String imageId, String content) async {
    try {
      await _apiService.addThreadToImage(imageId, content);
      await fetchFullImage(imageId);
    } catch (e) {
      debugPrint('Add thread to image error: $e');
    }
  }

  Future<void> addReplyToImageThread(
      String imageId, String threadId, String content) async {
    try {
      await _apiService.addReplyToImageThread(imageId, threadId, content);
      await fetchFullImage(imageId);
    } catch (e) {
      debugPrint('Add reply to image thread error: $e');
    }
  }

  Future<void> downloadImage(String url,
      {String filename = 'community-image.jpg'}) async {
    final status = await Permission.storage.request();
    if (status.isGranted) {
      try {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Download error: $e');
      }
    } else {
      debugPrint('Storage permission denied');
    }
  }

  String formatRelativeTime(DateTime date) {
    final now = DateTime.now();
    final diffInHours = now.difference(date).inHours;
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return '${diffInHours}h ago';
    return '${diffInHours ~/ 24}d ago';
  }

  void setNewMessage(String value) {
    newMessage = value;
    notifyListeners();
  }

  void setNewReply(String value) {
    newReply = value;
    notifyListeners();
  }

  void setSelectedThread(Thread? thread) {
    selectedThread = thread;
    notifyListeners();
  }

  void setSelectedImage(String? id) {
    selectedImage = null;
    if (id != null) fetchFullImage(id);
    notifyListeners();
  }
}

// Main Widget
class StudentCommunityPage extends StatefulWidget {
  StudentCommunityPage({super.key}); // Non-const constructor
  @override
  _StudentCommunityState createState() => _StudentCommunityState();
}

class _StudentCommunityState extends State<StudentCommunityPage>
    with TickerProviderStateMixin {
  late TabController _tabController; // Define TabController here

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: 2, vsync: this); // Initialize with vsync
  }

  @override
  void dispose() {
    _tabController.dispose(); // Clean up
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) return;
        Navigator.pop(context); // Handle back navigation
      },
      child: ChangeNotifierProvider(
        create: (_) => CommunityProvider(),
        child: Consumer<CommunityProvider>(
          builder: (context, provider, _) {
            if (provider.token == null) {
              return LoginScreen(provider: provider);
            }
            return Scaffold(
              backgroundColor: Colors.grey[50],
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(80),
                child: HeaderWidget(user: provider.user!),
              ),
              body: Column(
                children: [
                  TabSection(
                      provider: provider,
                      tabController: _tabController), // Pass TabController
                  Expanded(
                    child: TabBarView(
                      controller: _tabController, // Use local TabController
                      children: [
                        DiscussionsTab(provider: provider),
                        GalleryTab(provider: provider),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

// Login Screen
class LoginScreen extends StatelessWidget {
  final CommunityProvider provider;

  const LoginScreen({Key? key, required this.provider}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFE3F2FD), Color(0xFFBBDEFB)],
          ),
        ),
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              boxShadow: [
                BoxShadow(
                    blurRadius: 20, color: Colors.black.withOpacity(0.26)),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '🔐 Login Required',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Enter your JWT token to access the community.',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.indigo),
                    ),
                    labelText: 'Paste your JWT token',
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => provider.login(controller.text),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: const Text('Enter Community'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Header Widget
class HeaderWidget extends StatelessWidget implements PreferredSizeWidget {
  final User user;

  const HeaderWidget({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3), // Student color
        boxShadow: [
          BoxShadow(blurRadius: 10, color: Colors.black.withOpacity(0.26)),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back + Logo
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  const SizedBox(width: 16),
                  Row(
                    children: [
                      Stack(
                        children: [
                          const CircleAvatar(
                            radius: 24,
                            backgroundColor: Colors.white,
                            child: Text('🎓', style: TextStyle(fontSize: 24)),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border:
                                    Border.all(color: Colors.white, width: 2),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Student Community',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(blurRadius: 2, color: Colors.black26)
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // Active + Profile
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Active',
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w500)),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  GestureDetector(
                    onTap: () {
                      // Profile dropdown logic (show dialog if needed)
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      transform: Matrix4.identity()..scale(1.0),
                      child: CircleAvatar(
                        radius: 20,
                        backgroundColor:
                            const Color(0xFF9C27B0), // Counselor color
                        child: Text(
                          user.username[0].toUpperCase(),
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(80);
}

// Tab Section
class TabSection extends StatelessWidget {
  final CommunityProvider provider;
  final TabController tabController; // Added parameter

  const TabSection(
      {Key? key, required this.provider, required this.tabController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white.withOpacity(0.8),
      child: Column(
        children: [
          TabBar(
            controller: tabController, // Use passed TabController
            labelColor: Colors.indigo,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Colors.blue,
            tabs: const [
              Tab(icon: Icon(Icons.chat_bubble_outline), text: 'Discussions'),
              Tab(icon: Icon(Icons.photo_library_outlined), text: 'Gallery'),
            ],
          ),
          Container(
            height: 2,
            color: Colors.grey[200],
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              width: MediaQuery.of(context).size.width / 2,
              color: Colors.indigo[200],
            ),
          ),
        ],
      ),
    );
  }
}

// Discussions Tab
class DiscussionsTab extends StatefulWidget {
  final CommunityProvider provider;

  const DiscussionsTab({Key? key, required this.provider}) : super(key: key);

  @override
  _DiscussionsTabState createState() => _DiscussionsTabState();
}

class _DiscussionsTabState extends State<DiscussionsTab> {
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _replyController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Consumer<CommunityProvider>(
      builder: (context, provider, _) {
        if (provider.loadingThreads) {
          return const Center(child: SpinKitFadingCircle(color: Colors.indigo));
        }
        return Column(
          children: [
            // New Thread Input
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      onChanged: provider.setNewMessage,
                      decoration: const InputDecoration(
                        hintText: 'Start a new discussion...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: provider.newMessage.isNotEmpty
                        ? () {
                            provider.createThread(provider.newMessage);
                            _messageController.clear();
                          }
                        : null,
                    icon: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: provider.localThreads.length,
                itemBuilder: (context, index) {
                  final thread = provider.localThreads[index];
                  return Card(
                    margin:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: ExpansionTile(
                      title: Text(thread.content),
                      subtitle: Text(
                          '${thread.firstName} • ${provider.formatRelativeTime(thread.createdAt)}'),
                      children: [
                        // Replies List
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: thread.subthreads.length,
                          itemBuilder: (context, rIndex) {
                            final reply = thread.subthreads[rIndex];
                            return ListTile(
                              leading: CircleAvatar(
                                  child:
                                      Text(reply.firstName[0].toUpperCase())),
                              title: Text(reply.content),
                              subtitle: Text(
                                  provider.formatRelativeTime(reply.createdAt)),
                            );
                          },
                        ),
                        // Reply Input (if selected)
                        if (provider.selectedThread?.id == thread.id)
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _replyController,
                                    onChanged: provider.setNewReply,
                                    decoration: const InputDecoration(
                                        hintText: 'Reply...'),
                                  ),
                                ),
                                IconButton(
                                  onPressed: provider.newReply.isNotEmpty
                                      ? () {
                                          provider.addReply(
                                              provider.newReply, thread.id);
                                          _replyController.clear();
                                        }
                                      : null,
                                  icon: const Icon(Icons.send),
                                ),
                              ],
                            ),
                          ),
                      ],
                      onExpansionChanged: (expanded) {
                        if (expanded) provider.setSelectedThread(thread);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}

// Gallery Tab
class GalleryTab extends StatefulWidget {
  final CommunityProvider provider;

  const GalleryTab({Key? key, required this.provider}) : super(key: key);

  @override
  _GalleryTabState createState() => _GalleryTabState();
}

class _GalleryTabState extends State<GalleryTab> {
  final ImagePicker _picker = ImagePicker();

  Future<void> _uploadImage() async {
    final XFile? file = await _picker.pickImage(source: ImageSource.gallery);
    if (file != null) {
      await widget.provider.uploadImage(file);
    }
  }

  void _showImageModal(String id) {
    widget.provider.setSelectedImage(id);
    showDialog(
      context: context,
      builder: (context) => Consumer<CommunityProvider>(
        builder: (context, provider, _) {
          if (provider.loadingFullImage || provider.selectedImage == null) {
            return const AlertDialog(
              content: SpinKitFadingCircle(color: Colors.indigo),
            );
          }
          final fullImage = provider.selectedImage!;
          return AlertDialog(
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Full Image
                  GestureDetector(
                    onTap: () => provider.downloadImage(fullImage.url),
                    child: CachedNetworkImage(
                      imageUrl: fullImage.url,
                      height: 300,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Threads on Image
                  ...?fullImage.threads?.map((thread) => ListTile(
                        title: Text(thread.content),
                        subtitle:
                            Text(provider.formatRelativeTime(thread.createdAt)),
                      )),
                  // Add Thread Input (Simplified)
                  TextField(
                    onSubmitted: (content) {
                      provider.addThreadToImage(fullImage.id, content);
                      Navigator.pop(context);
                    },
                    decoration: const InputDecoration(
                        hintText: 'Add thread to image...'),
                  ),
                  // Add Reply (Assume first thread for simplicity)
                  if (fullImage.threads?.isNotEmpty ?? false)
                    TextField(
                      onSubmitted: (content) {
                        provider.addReplyToImageThread(
                            fullImage.id, fullImage.threads!.first.id, content);
                        Navigator.pop(context);
                      },
                      decoration: const InputDecoration(
                          hintText: 'Add reply to thread...'),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CommunityProvider>(
      builder: (context, provider, _) {
        if (provider.loadingImages) {
          return const Center(child: SpinKitFadingCircle(color: Colors.indigo));
        }
        return Column(
          children: [
            // Upload Button
            Padding(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton.icon(
                onPressed: _uploadImage,
                icon: const Icon(Icons.upload),
                label: const Text('Upload Image'),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: provider.images.length,
                itemBuilder: (context, index) {
                  final image = provider.images[index];
                  return GestureDetector(
                    onTap: () => _showImageModal(image.id),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: CachedNetworkImage(
                            imageUrl: image.url,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        Positioned(
                          bottom: 8,
                          right: 8,
                          child: IconButton(
                            onPressed: () => provider.downloadImage(image.url),
                            icon:
                                const Icon(Icons.download, color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
